<template>
  <div class="login-container">
    <div class="login-card-wrapper">
      <!-- Left Side - Welcome Section -->
      <div class="welcome-section">
        <div class="decorative-elements">
          <!-- Decorative Plus Signs -->
          <div class="plus-icon plus-1">+</div>
          <div class="plus-icon plus-2">+</div>

          <!-- Decorative Dots -->
          <div class="dots-pattern">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>

          <!-- Decorative Circles -->
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>

          <!-- Flowing Lines -->
          <svg class="flowing-lines" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path d="M20,100 Q60,50 100,100 T180,100" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
            <path d="M30,120 Q70,70 110,120 T190,120" stroke="rgba(255,255,255,0.2)" stroke-width="1.5" fill="none"/>
            <path d="M10,140 Q50,90 90,140 T170,140" stroke="rgba(255,255,255,0.25)" stroke-width="1.8" fill="none"/>
          </svg>
        </div>

        <div class="welcome-content">
          <h1 class="welcome-title">Welcome back!</h1>
          <p class="welcome-subtitle">You can sign in to access with your existing account.</p>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="form-section">
        <div class="form-content">
          <div class="form-header">
            <h2 class="form-title">Sign In</h2>
          </div>

          <a-form
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
            layout="vertical"
            class="login-form"
          >
            <a-form-item
              name="username"
              class="form-item"
            >
              <a-input
                v-model:value="loginForm.username"
                placeholder="Username or email"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <UserOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item
              name="password"
              class="form-item"
            >
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="Password"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <LockOutlined class="input-icon" />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item class="form-item">
              <div class="form-options">
                <a-checkbox v-model:checked="loginForm.remember" class="remember-checkbox">
                  Remember me
                </a-checkbox>
                <a href="#" class="forgot-password">Forgot password?</a>
              </div>
            </a-form-item>

            <a-form-item class="form-item">
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                :loading="loading"
                class="login-button"
                block
              >
                Sign In
              </a-button>
            </a-form-item>
          </a-form>

          <div class="form-footer">
            <p class="footer-text">
              New here? <a href="#" class="create-account">Create an Account</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Reactive data
const loading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const rules = {
  username: [
    { required: true, message: 'Please enter your username!', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters!', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter your password!', trigger: 'blur' },
    { min: 6, message: 'Password must be at least 6 characters!', trigger: 'blur' }
  ]
}

// Handle login
const handleLogin = async (values) => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    console.log('Login values:', values)
    message.success('Login successful!')

    // Here you would typically:
    // 1. Call your authentication API
    // 2. Store the token
    // 3. Redirect to dashboard

  } catch (error) {
    message.error('Login failed!')
  } finally {
    loading.value = false
  }
}

// Handle login failed
const handleLoginFailed = (errorInfo) => {
  console.log('Failed:', errorInfo)
  message.error('Please check your login credentials!')
}
</script>

<style scoped>
/* Color Variables */
:root {
  --primary-navy: #1E3A8A;
  --primary-lime: #9ACD32;
  --white: #FFFFFF;
  --light-gray: #F8FAFC;
  --border-gray: #E2E8F0;
  --text-gray: #64748B;
  --text-dark: #1E293B;
  --gradient-start: #1E3A8A;
  --gradient-end: #2563EB;
}

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-card-wrapper {
  background: var(--white);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  max-width: 900px;
  width: 100%;
  min-height: 600px;
  position: relative;
}

/* Left Side - Welcome Section */
.welcome-section {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-navy) 0%, var(--gradient-end) 100%);
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.plus-icon {
  position: absolute;
  color: rgba(255, 255, 255, 0.4);
  font-size: 24px;
  font-weight: 300;
}

.plus-1 {
  top: 80px;
  right: 80px;
}

.plus-2 {
  bottom: 120px;
  left: 60px;
}

.dots-pattern {
  position: absolute;
  top: 60px;
  right: 140px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.dot {
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

.circle {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.circle-1 {
  width: 60px;
  height: 60px;
  bottom: 200px;
  right: 40px;
}

.circle-2 {
  width: 40px;
  height: 40px;
  top: 160px;
  left: 40px;
}

.flowing-lines {
  position: absolute;
  bottom: 60px;
  left: 20px;
  width: 180px;
  height: 120px;
  opacity: 0.6;
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.welcome-title {
  font-size: 42px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 16px;
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  max-width: 280px;
}

/* Right Side - Form Section */
.form-section {
  flex: 1;
  background: var(--white);
  padding: 60px 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-content {
  width: 100%;
  max-width: 320px;
}

.form-header {
  margin-bottom: 40px;
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0;
  text-align: left;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-of-type {
  margin-bottom: 0;
}

.custom-input {
  border-radius: 12px;
  border: 1px solid var(--border-gray);
  transition: all 0.3s ease;
  background: var(--light-gray);
  height: 50px;
}

.custom-input:hover {
  border-color: var(--primary-lime);
  background: var(--white);
}

.custom-input:focus,
.custom-input:focus-within {
  border-color: var(--primary-lime);
  background: var(--white);
  box-shadow: 0 0 0 3px rgba(154, 205, 50, 0.1);
}

.input-icon {
  color: var(--text-gray);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.remember-checkbox :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: var(--primary-lime);
  border-color: var(--primary-lime);
}

.remember-checkbox :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
  color: var(--text-gray);
}

.forgot-password {
  color: var(--text-gray);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-navy);
}

.login-button {
  background: var(--primary-navy);
  border-color: var(--primary-navy);
  border-radius: 12px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.login-button:hover {
  background: var(--gradient-end);
  border-color: var(--gradient-end);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.form-footer {
  text-align: center;
}

.footer-text {
  color: var(--text-gray);
  font-size: 14px;
  margin: 0;
}

.create-account {
  color: var(--primary-navy);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.create-account:hover {
  color: var(--primary-lime);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .login-card-wrapper {
    max-width: 800px;
    min-height: 550px;
  }

  .welcome-section {
    padding: 50px 40px;
  }

  .form-section {
    padding: 50px 40px;
  }

  .welcome-title {
    font-size: 36px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .form-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 15px;
  }

  .login-card-wrapper {
    flex-direction: column;
    max-width: 500px;
    min-height: auto;
  }

  .welcome-section {
    padding: 40px 30px;
    min-height: 300px;
  }

  .form-section {
    padding: 40px 30px;
  }

  .welcome-title {
    font-size: 32px;
  }

  .welcome-subtitle {
    font-size: 16px;
    max-width: none;
  }

  .form-title {
    font-size: 26px;
    text-align: center;
  }

  .form-content {
    max-width: 100%;
  }

  .plus-1 {
    top: 40px;
    right: 40px;
  }

  .plus-2 {
    bottom: 60px;
    left: 30px;
  }

  .dots-pattern {
    top: 40px;
    right: 80px;
  }

  .circle-1 {
    bottom: 100px;
    right: 20px;
  }

  .circle-2 {
    top: 80px;
    left: 20px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .welcome-section {
    padding: 30px 20px;
    min-height: 250px;
  }

  .form-section {
    padding: 30px 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-options {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .custom-input {
    height: 45px;
  }

  .login-button {
    height: 45px;
  }
}
</style>
