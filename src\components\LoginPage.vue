<template>
  <div class="login-container">
    <div class="login-card-wrapper">
      <!-- Left Side - Welcome Section -->
      <div class="welcome-section">
        <div class="decorative-elements">
          <!-- Decorative Plus Signs -->
          <div class="plus-icon plus-1">+</div>
          <div class="plus-icon plus-2">+</div>

          <!-- Decorative Dots -->
          <div class="dots-pattern">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>

          <!-- Decorative Circles -->
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>

          <!-- Flowing Lines -->
          <svg class="flowing-lines" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path d="M20,100 Q60,50 100,100 T180,100" stroke="rgba(154,205,50,0.3)" stroke-width="2" fill="none"/>
            <path d="M30,120 Q70,70 110,120 T190,120" stroke="rgba(154,205,50,0.2)" stroke-width="1.5" fill="none"/>
            <path d="M10,140 Q50,90 90,140 T170,140" stroke="rgba(154,205,50,0.25)" stroke-width="1.8" fill="none"/>
          </svg>
        </div>

        <!-- Warehouse Illustration -->
        <div class="warehouse-illustration">
          <svg width="280" height="200" viewBox="0 0 280 200" class="warehouse-svg">
            <!-- Background -->
            <rect width="280" height="200" fill="url(#warehouseGradient)"/>

            <!-- Ground -->
            <rect x="0" y="160" width="280" height="40" fill="rgba(154,205,50,0.2)"/>

            <!-- Main Warehouse Building -->
            <polygon points="40,160 40,80 140,60 240,80 240,160" fill="rgba(255,255,255,0.15)" stroke="rgba(154,205,50,0.4)" stroke-width="2"/>

            <!-- Roof -->
            <polygon points="35,80 140,55 245,80 240,80 140,60 40,80" fill="rgba(154,205,50,0.8)"/>

            <!-- Front Wall -->
            <rect x="40" y="80" width="200" height="80" fill="rgba(255,255,255,0.1)"/>

            <!-- Large Door -->
            <rect x="60" y="120" width="50" height="40" fill="rgba(30,58,138,0.3)" stroke="rgba(154,205,50,0.6)" stroke-width="2"/>

            <!-- Windows -->
            <rect x="130" y="95" width="20" height="15" fill="rgba(154,205,50,0.6)"/>
            <rect x="160" y="95" width="20" height="15" fill="rgba(154,205,50,0.6)"/>
            <rect x="190" y="95" width="20" height="15" fill="rgba(154,205,50,0.6)"/>

            <!-- Loading Dock -->
            <rect x="130" y="130" width="40" height="30" fill="rgba(30,58,138,0.2)"/>

            <!-- Forklift -->
            <g transform="translate(180, 140)">
              <rect x="0" y="10" width="18" height="10" fill="rgba(154,205,50,0.8)"/>
              <rect x="14" y="3" width="6" height="17" fill="rgba(154,205,50,0.8)"/>
              <circle cx="4" cy="17" r="3" fill="rgba(255,255,255,0.6)"/>
              <circle cx="14" cy="17" r="3" fill="rgba(255,255,255,0.6)"/>
            </g>

            <!-- Shipping Containers -->
            <rect x="200" y="140" width="30" height="15" fill="rgba(30,58,138,0.3)"/>
            <rect x="203" y="143" width="24" height="9" fill="rgba(154,205,50,0.4)"/>

            <!-- Gradients -->
            <defs>
              <linearGradient id="warehouseGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:rgba(30,58,138,0.1);stop-opacity:1" />
                <stop offset="100%" style="stop-color:rgba(30,58,138,0.05);stop-opacity:1" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div class="welcome-content">
          <h1 class="welcome-title">Welcome back!</h1>
          <p class="welcome-subtitle">You can sign in to access with your existing warehouse management account.</p>

          <!-- Warehouse Features -->
          <div class="warehouse-features">
            <div class="feature-badge">
              <div class="feature-icon">📦</div>
              <span>Inventory</span>
            </div>
            <div class="feature-badge">
              <div class="feature-icon">🚛</div>
              <span>Shipping</span>
            </div>
            <div class="feature-badge">
              <div class="feature-icon">📊</div>
              <span>Analytics</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side - Login Form -->
      <div class="form-section">
        <div class="form-content">
          <div class="form-header">
            <h2 class="form-title">Sign In</h2>
          </div>

          <a-form
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
            layout="vertical"
            class="login-form"
          >
            <a-form-item
              name="username"
              class="form-item"
            >
              <a-input
                v-model:value="loginForm.username"
                placeholder="Username or email"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <UserOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item
              name="password"
              class="form-item"
            >
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="Password"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <LockOutlined class="input-icon" />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item class="form-item">
              <div class="form-options">
                <a-checkbox v-model:checked="loginForm.remember" class="remember-checkbox">
                  Remember me
                </a-checkbox>
                <a href="#" class="forgot-password">Forgot password?</a>
              </div>
            </a-form-item>

            <a-form-item class="form-item">
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                :loading="loading"
                class="login-button"
                block
              >
                Sign In
              </a-button>
            </a-form-item>
          </a-form>

          <div class="form-footer">
            <p class="footer-text">
              New here? <a href="#" class="create-account">Create an Account</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Reactive data
const loading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const rules = {
  username: [
    { required: true, message: 'Please enter your username!', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters!', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter your password!', trigger: 'blur' },
    { min: 6, message: 'Password must be at least 6 characters!', trigger: 'blur' }
  ]
}

// Handle login
const handleLogin = async (values) => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    console.log('Login values:', values)
    message.success('Login successful!')

    // Here you would typically:
    // 1. Call your authentication API
    // 2. Store the token
    // 3. Redirect to dashboard

  } catch (error) {
    message.error('Login failed!')
  } finally {
    loading.value = false
  }
}

// Handle login failed
const handleLoginFailed = (errorInfo) => {
  console.log('Failed:', errorInfo)
  message.error('Please check your login credentials!')
}
</script>

<style scoped>
/* Color Variables */
:root {
  --primary-navy: #1E3A8A;
  --primary-lime: #9ACD32;
  --white: #FFFFFF;
  --light-gray: #F8FAFC;
  --border-gray: #E2E8F0;
  --text-gray: #64748B;
  --text-dark: #1E293B;
  --gradient-start: #1E3A8A;
  --gradient-end: #2563EB;
}

.login-container {
  min-height: 100vh;
  height: 100vh;
  background: linear-gradient(135deg, #1a025b 0%, #8BC34A 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.login-card-wrapper {
  background: var(--white);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  width: 70vw;
  max-width: 1400px;
  min-width: 1000px;
  height: 700px;
  position: relative;
  margin: 0 auto;
  z-index: 1;
}

/* Left Side - Welcome Section */
.welcome-section {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-navy) 0%, var(--gradient-end) 100%);
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.plus-icon {
  position: absolute;
  color: rgba(154, 205, 50, 0.4);
  font-size: 24px;
  font-weight: 300;
}

.plus-1 {
  top: 80px;
  right: 80px;
}

.plus-2 {
  bottom: 120px;
  left: 60px;
}

.dots-pattern {
  position: absolute;
  top: 60px;
  right: 140px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.dot {
  width: 4px;
  height: 4px;
  background: rgba(154, 205, 50, 0.5);
  border-radius: 50%;
}

.circle {
  position: absolute;
  border: 2px solid rgba(154, 205, 50, 0.3);
  border-radius: 50%;
}

.circle-1 {
  width: 60px;
  height: 60px;
  bottom: 200px;
  right: 40px;
}

.circle-2 {
  width: 40px;
  height: 40px;
  top: 160px;
  left: 40px;
}

.flowing-lines {
  position: absolute;
  bottom: 60px;
  left: 20px;
  width: 180px;
  height: 120px;
  opacity: 0.6;
}

/* Warehouse Illustration */
.warehouse-illustration {
  position: relative;
  z-index: 2;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.warehouse-svg {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

.welcome-content {
  position: relative;
  z-index: 2;
}

.welcome-title {
  font-size: 48px;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 20px;
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0 0 30px 0;
  max-width: 350px;
}

/* Warehouse Features */
.warehouse-features {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.feature-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(154, 205, 50, 0.2);
  border: 1px solid rgba(154, 205, 50, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.feature-badge:hover {
  background: rgba(154, 205, 50, 0.3);
  transform: translateY(-2px);
}

.feature-badge .feature-icon {
  font-size: 16px;
}

.feature-badge span {
  font-size: 13px;
  font-weight: 500;
  color: var(--white);
}

/* Right Side - Form Section */
.form-section {
  flex: 1;
  background: var(--white);
  padding: 60px 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  overflow-y: auto;
}

.form-content {
  width: 100%;
  max-width: 320px;
}

.form-header {
  margin-bottom: 40px;
}

.form-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0;
  text-align: left;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-of-type {
  margin-bottom: 0;
}

.custom-input {
  border-radius: 25px;
  border: 2px solid var(--border-gray);
  transition: all 0.3s ease;
  background: var(--light-gray);
  height: 50px;
}

.custom-input:hover {
  border-color: #8BC34A;
  background: var(--white);
}

.custom-input:focus,
.custom-input:focus-within {
  border-color: #8BC34A;
  background: var(--white);
  box-shadow: 0 0 0 3px rgba(139, 195, 74, 0.1);
}

.input-icon {
  color: var(--text-gray);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.remember-checkbox :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #8BC34A;
  border-color: #8BC34A;
}

.remember-checkbox :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
  color: var(--text-gray);
}

.forgot-password {
  color: var(--text-gray);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #1a025b;
}

.login-button {
  background: #1a025b;
  border-color: #1a025b;
  border-radius: 25px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.login-button:hover {
  background: #2a0a6b;
  border-color: #2a0a6b;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(26, 2, 91, 0.3);
}

.form-footer {
  text-align: center;
}

.footer-text {
  color: var(--text-gray);
  font-size: 14px;
  margin: 0;
}

.create-account {
  color: #1a025b;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.create-account:hover {
  color: #8BC34A;
}

/* Responsive Design - Desktop Only */
@media (max-width: 1600px) {
  .login-card-wrapper {
    width: 75vw;
    max-width: 1200px;
  }
}

@media (max-width: 1400px) {
  .login-card-wrapper {
    width: 80vw;
    max-width: 1100px;
    height: 650px;
  }

  .welcome-title {
    font-size: 42px;
  }

  .form-title {
    font-size: 28px;
  }
}

@media (max-width: 1200px) {
  .login-card-wrapper {
    width: 85vw;
    max-width: 1000px;
    min-width: 900px;
    height: 600px;
  }

  .welcome-section {
    padding: 50px 40px;
  }

  .form-section {
    padding: 50px 40px;
  }

  .welcome-title {
    font-size: 40px;
  }

  .welcome-subtitle {
    font-size: 16px;
    max-width: 300px;
  }

  .form-title {
    font-size: 26px;
  }

  .warehouse-svg {
    width: 240px;
    height: 170px;
  }
}
</style>
