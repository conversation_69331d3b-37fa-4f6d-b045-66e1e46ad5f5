<template>
  <div class="login-container">
    <div class="login-background">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-section">
            <div class="warehouse-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7V17C2 17.55 2.45 18 3 18H21C21.55 18 22 17.55 22 17V7L12 2Z" fill="#9ACD32"/>
                <path d="M12 2V18" stroke="#1E3A8A" stroke-width="2"/>
                <path d="M2 7H22" stroke="#1E3A8A" stroke-width="2"/>
                <path d="M7 12H17" stroke="#1E3A8A" stroke-width="1.5"/>
                <path d="M7 15H17" stroke="#1E3A8A" stroke-width="1.5"/>
              </svg>
            </div>
            <h1 class="system-title"><PERSON><PERSON> T<PERSON>ống <PERSON></h1>
            <p class="system-subtitle">Warehouse Management System</p>
          </div>
        </div>

        <div class="login-form-section">
          <a-form
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
            layout="vertical"
            class="login-form"
          >
            <a-form-item
              label="Tên đăng nhập"
              name="username"
              class="form-item"
            >
              <a-input
                v-model:value="loginForm.username"
                placeholder="Nhập tên đăng nhập"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <UserOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item
              label="Mật khẩu"
              name="password"
              class="form-item"
            >
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="Nhập mật khẩu"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <LockOutlined class="input-icon" />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item class="form-item">
              <div class="form-options">
                <a-checkbox v-model:checked="loginForm.remember" class="remember-checkbox">
                  Ghi nhớ đăng nhập
                </a-checkbox>
                <a href="#" class="forgot-password">Quên mật khẩu?</a>
              </div>
            </a-form-item>

            <a-form-item class="form-item">
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                :loading="loading"
                class="login-button"
                block
              >
                Đăng Nhập
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <div class="login-footer">
          <p class="footer-text">
            © 2024 Warehouse Management System. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Reactive data
const loading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const rules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập!', trigger: 'blur' },
    { min: 3, message: 'Tên đăng nhập phải có ít nhất 3 ký tự!', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Vui lòng nhập mật khẩu!', trigger: 'blur' },
    { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự!', trigger: 'blur' }
  ]
}

// Handle login
const handleLogin = async (values) => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('Login values:', values)
    message.success('Đăng nhập thành công!')
    
    // Here you would typically:
    // 1. Call your authentication API
    // 2. Store the token
    // 3. Redirect to dashboard
    
  } catch (error) {
    message.error('Đăng nhập thất bại!')
  } finally {
    loading.value = false
  }
}

// Handle login failed
const handleLoginFailed = (errorInfo) => {
  console.log('Failed:', errorInfo)
  message.error('Vui lòng kiểm tra thông tin đăng nhập!')
}
</script>

<style scoped>
/* Color Variables */
:root {
  --primary-navy: #1E3A8A;
  --primary-lime: #9ACD32;
  --white: #FFFFFF;
  --light-gray: #F8FAFC;
  --border-gray: #E2E8F0;
  --text-gray: #64748B;
  --text-dark: #1E293B;
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-navy) 0%, #2563EB 100%);
  padding: 20px;
}

.login-background {
  width: 100%;
  max-width: 450px;
  position: relative;
}

.login-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;
}

.login-header {
  background: linear-gradient(135deg, var(--white) 0%, var(--light-gray) 100%);
  padding: 40px 30px 30px;
  text-align: center;
  border-bottom: 1px solid var(--border-gray);
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.warehouse-icon {
  background: var(--white);
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--primary-lime);
}

.system-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-navy);
  margin: 0;
  line-height: 1.2;
}

.system-subtitle {
  font-size: 14px;
  color: var(--text-gray);
  margin: 0;
  font-weight: 500;
}

.login-form-section {
  padding: 30px;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 20px;
}

.form-item :deep(.ant-form-item-label > label) {
  color: var(--text-dark);
  font-weight: 600;
  font-size: 14px;
}

.custom-input {
  border-radius: 8px;
  border: 2px solid var(--border-gray);
  transition: all 0.3s ease;
}

.custom-input:hover {
  border-color: var(--primary-lime);
}

.custom-input:focus,
.custom-input:focus-within {
  border-color: var(--primary-lime);
  box-shadow: 0 0 0 3px rgba(154, 205, 50, 0.1);
}

.input-icon {
  color: var(--text-gray);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remember-checkbox :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: var(--primary-lime);
  border-color: var(--primary-lime);
}

.forgot-password {
  color: var(--primary-navy);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-lime);
}

.login-button {
  background: var(--primary-navy);
  border-color: var(--primary-navy);
  border-radius: 8px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: #1E40AF;
  border-color: #1E40AF;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.login-footer {
  background: var(--light-gray);
  padding: 20px 30px;
  text-align: center;
  border-top: 1px solid var(--border-gray);
}

.footer-text {
  color: var(--text-gray);
  font-size: 12px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-header {
    padding: 30px 20px 20px;
  }
  
  .login-form-section {
    padding: 20px;
  }
  
  .system-title {
    font-size: 20px;
  }
  
  .form-options {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
