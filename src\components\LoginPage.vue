<template>
  <div class="login-container">
    <!-- Left Side - Warehouse Image -->
    <div class="login-left">
      <div class="warehouse-illustration">
        <div class="warehouse-scene">
          <!-- Warehouse Building -->
          <svg width="400" height="300" viewBox="0 0 400 300" class="warehouse-svg">
            <!-- Background -->
            <rect width="400" height="300" fill="url(#skyGradient)"/>

            <!-- Ground -->
            <rect x="0" y="250" width="400" height="50" fill="#4A5568"/>

            <!-- Main Warehouse Building -->
            <polygon points="50,250 50,120 200,80 350,120 350,250" fill="#2D3748" stroke="#1A202C" stroke-width="2"/>

            <!-- Roof -->
            <polygon points="40,120 200,70 360,120 350,120 200,80 50,120" fill="#9ACD32"/>

            <!-- Front Wall -->
            <rect x="50" y="120" width="300" height="130" fill="#4A5568"/>

            <!-- Large Door -->
            <rect x="80" y="180" width="80" height="70" fill="#2D3748" stroke="#1E3A8A" stroke-width="3"/>
            <rect x="85" y="185" width="70" height="60" fill="#1E3A8A"/>

            <!-- Windows -->
            <rect x="200" y="140" width="30" height="25" fill="#9ACD32" opacity="0.8"/>
            <rect x="250" y="140" width="30" height="25" fill="#9ACD32" opacity="0.8"/>
            <rect x="300" y="140" width="30" height="25" fill="#9ACD32" opacity="0.8"/>

            <!-- Loading Dock -->
            <rect x="180" y="200" width="60" height="50" fill="#1E3A8A"/>
            <rect x="185" y="205" width="50" height="40" fill="#2D3748"/>

            <!-- Forklift -->
            <g transform="translate(260, 220)">
              <rect x="0" y="15" width="25" height="15" fill="#9ACD32"/>
              <rect x="20" y="5" width="8" height="25" fill="#9ACD32"/>
              <circle cx="5" cy="25" r="4" fill="#2D3748"/>
              <circle cx="20" cy="25" r="4" fill="#2D3748"/>
              <rect x="25" y="8" width="15" height="4" fill="#1E3A8A"/>
            </g>

            <!-- Shipping Containers -->
            <rect x="300" y="220" width="40" height="20" fill="#1E3A8A"/>
            <rect x="305" y="225" width="30" height="10" fill="#9ACD32"/>

            <!-- Clouds -->
            <ellipse cx="80" cy="40" rx="25" ry="15" fill="white" opacity="0.7"/>
            <ellipse cx="320" cy="30" rx="30" ry="18" fill="white" opacity="0.7"/>

            <!-- Gradients -->
            <defs>
              <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        <div class="illustration-content">
          <h2 class="illustration-title">Warehouse Management System</h2>
          <p class="illustration-subtitle">Streamline your inventory operations with our comprehensive warehouse management solution</p>

          <div class="features-list">
            <div class="feature-item">
              <div class="feature-icon">📦</div>
              <span>Inventory Tracking</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🚛</div>
              <span>Shipping Management</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <span>Real-time Analytics</span>
            </div>
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <span>Automated Workflows</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side - Login Form -->
    <div class="login-right">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-section">
            <div class="warehouse-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7V17C2 17.55 2.45 18 3 18H21C21.55 18 22 17.55 22 17V7L12 2Z" fill="#9ACD32"/>
                <path d="M12 2V18" stroke="#1E3A8A" stroke-width="2"/>
                <path d="M2 7H22" stroke="#1E3A8A" stroke-width="2"/>
                <path d="M7 12H17" stroke="#1E3A8A" stroke-width="1.5"/>
                <path d="M7 15H17" stroke="#1E3A8A" stroke-width="1.5"/>
              </svg>
            </div>
            <h1 class="system-title">Welcome Back</h1>
            <p class="system-subtitle">Sign in to your warehouse account</p>
          </div>
        </div>

        <div class="login-form-section">
          <a-form
            :model="loginForm"
            :rules="rules"
            @finish="handleLogin"
            @finishFailed="handleLoginFailed"
            layout="vertical"
            class="login-form"
          >
            <a-form-item
              label="Username"
              name="username"
              class="form-item"
            >
              <a-input
                v-model:value="loginForm.username"
                placeholder="Enter your username"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <UserOutlined class="input-icon" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item
              label="Password"
              name="password"
              class="form-item"
            >
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="Enter your password"
                size="large"
                class="custom-input"
              >
                <template #prefix>
                  <LockOutlined class="input-icon" />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item class="form-item">
              <div class="form-options">
                <a-checkbox v-model:checked="loginForm.remember" class="remember-checkbox">
                  Remember me
                </a-checkbox>
                <a href="#" class="forgot-password">Forgot password?</a>
              </div>
            </a-form-item>

            <a-form-item class="form-item">
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                :loading="loading"
                class="login-button"
                block
              >
                Sign In
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <div class="login-footer">
          <p class="footer-text">
            © 2024 Warehouse Management System. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Reactive data
const loading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const rules = {
  username: [
    { required: true, message: 'Please enter your username!', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters!', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Please enter your password!', trigger: 'blur' },
    { min: 6, message: 'Password must be at least 6 characters!', trigger: 'blur' }
  ]
}

// Handle login
const handleLogin = async (values) => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    console.log('Login values:', values)
    message.success('Login successful!')

    // Here you would typically:
    // 1. Call your authentication API
    // 2. Store the token
    // 3. Redirect to dashboard

  } catch (error) {
    message.error('Login failed!')
  } finally {
    loading.value = false
  }
}

// Handle login failed
const handleLoginFailed = (errorInfo) => {
  console.log('Failed:', errorInfo)
  message.error('Please check your login credentials!')
}
</script>

<style scoped>
/* Color Variables */
:root {
  --primary-navy: #1E3A8A;
  --primary-lime: #9ACD32;
  --white: #FFFFFF;
  --light-gray: #F8FAFC;
  --border-gray: #E2E8F0;
  --text-gray: #64748B;
  --text-dark: #1E293B;
}

.login-container {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  width: 100vw;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

/* Left Side - Warehouse Illustration */
.login-left {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-navy) 0%, #2563EB 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
  overflow-y: auto;
}

.warehouse-illustration {
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.warehouse-scene {
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
}

.warehouse-svg {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

.illustration-content {
  color: var(--white);
}

.illustration-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.2;
}

.illustration-subtitle {
  font-size: 18px;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.5;
}

.features-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.feature-item span {
  font-size: 14px;
  font-weight: 500;
}

/* Right Side - Login Form */
.login-right {
  flex: 0 0 500px;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  overflow-y: auto;
  min-height: 100vh;
}

.login-card {
  width: 100%;
  max-width: 400px;
  margin: auto;
}

.login-header {
  padding: 30px 0 20px;
  text-align: center;
  border-bottom: 1px solid var(--border-gray);
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.warehouse-icon {
  background: var(--light-gray);
  padding: 12px;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--primary-lime);
}

.system-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-navy);
  margin: 0;
  line-height: 1.2;
}

.system-subtitle {
  font-size: 16px;
  color: var(--text-gray);
  margin: 0;
  font-weight: 500;
}

.login-form-section {
  padding: 30px 0;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 24px;
}

.form-item :deep(.ant-form-item-label > label) {
  color: var(--text-dark);
  font-weight: 600;
  font-size: 14px;
}

.custom-input {
  border-radius: 8px;
  border: 2px solid var(--border-gray);
  transition: all 0.3s ease;
}

.custom-input:hover {
  border-color: var(--primary-lime);
}

.custom-input:focus,
.custom-input:focus-within {
  border-color: var(--primary-lime);
  box-shadow: 0 0 0 3px rgba(154, 205, 50, 0.1);
}

.input-icon {
  color: var(--text-gray);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remember-checkbox :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: var(--primary-lime);
  border-color: var(--primary-lime);
}

.forgot-password {
  color: var(--primary-navy);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-lime);
}

.login-button {
  background: var(--primary-navy);
  border-color: var(--primary-navy);
  border-radius: 8px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: #1E40AF;
  border-color: #1E40AF;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.login-footer {
  padding: 20px 0;
  text-align: center;
  border-top: 1px solid var(--border-gray);
}

.footer-text {
  color: var(--text-gray);
  font-size: 12px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .login-left {
    flex: 0 0 45%;
  }

  .login-right {
    flex: 0 0 55%;
  }

  .illustration-title {
    font-size: 28px;
  }

  .illustration-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    position: relative;
    height: auto;
    min-height: 100vh;
  }

  .login-left {
    flex: 0 0 50vh;
    padding: 20px;
    min-height: 50vh;
  }

  .login-right {
    flex: 1;
    padding: 20px;
    min-height: 50vh;
    position: relative;
  }

  .warehouse-scene {
    margin-bottom: 20px;
  }

  .warehouse-svg {
    width: 300px;
    height: 200px;
  }

  .illustration-title {
    font-size: 24px;
  }

  .illustration-subtitle {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .features-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .feature-item {
    padding: 12px;
  }

  .system-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .login-container {
    position: relative;
    height: auto;
  }

  .login-left {
    flex: 0 0 40vh;
    padding: 15px;
    min-height: 40vh;
  }

  .login-right {
    padding: 15px;
    min-height: 60vh;
  }

  .warehouse-svg {
    width: 250px;
    height: 150px;
  }

  .illustration-title {
    font-size: 20px;
  }

  .illustration-subtitle {
    font-size: 12px;
  }

  .system-title {
    font-size: 20px;
  }

  .form-options {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
