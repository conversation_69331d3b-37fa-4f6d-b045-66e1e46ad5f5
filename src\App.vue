<script setup>
import HelloWorld from './components/HelloWorld.vue';
import TheWelcome from './components/TheWelcome.vue';
</script>

<template>
  <div class="result-video">
    <video
      ref="videoPlayer"
      width="640"
      height="360"
      controls
      controlsList="nodownload noplaybackrate"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
    >
      <source :src="videoSrc" type="video/mp4" />
      Your browser does not support the video tag.
    </video>

    <p>Current Time: {{ formattedCurrentTime }}</p>
    <p>Duration: {{ formattedDuration }}</p>

    <!-- Scrolling List Display -->
    <div class="scroll-container" ref="scrollContainer">
      <div
        v-for="(item, index) in items"
        :key="index"
        ref="listItems"
        :class="{ active: isActive(item) }"
      >
        {{ item.textValue }}
      </div>
    </div>
  </div>
</template>

<script>
import videoSrc from '@/assets/videos/my-video.mp4';
export default {
  data() {
    return {
      currentTime: 0,
      duration: 0,
      videoSrc: videoSrc,
      items: [
        { uttstart_time: '00:00:00', textValue: 'Scene 00:00:00' },
        { uttstart_time: '00:00:03', textValue: 'Scene 00:00:03' },
        { uttstart_time: '00:00:20', textValue: 'Scene 00:00:20' },
        { uttstart_time: '00:00:42', textValue: 'Scene 00:00:42' },
        { uttstart_time: '00:00:25', textValue: 'Scene 00:00:25' },
        { uttstart_time: '00:00:30', textValue: 'Scene 00:00:30' },
        { uttstart_time: '00:00:35', textValue: 'Scene 00:00:35' },
        { uttstart_time: '00:00:40', textValue: 'Scene 00:00:40' },
        { uttstart_time: '00:00:45', textValue: 'Scene 00:00:45' },
        { uttstart_time: '00:00:50', textValue: 'Scene 00:00:50' },
        { uttstart_time: '00:00:55', textValue: 'Scene 00:00:55' },
        { uttstart_time: '00:01:00', textValue: 'Scene 00:01:00' },
        { uttstart_time: '00:01:03', textValue: 'Scene 00:01:03' },
        { uttstart_time: '00:01:20', textValue: 'Scene 00:01:20' },
      ], // List of items with their keyTimes and corresponding text
      currentItemIndex: -1, // Track the currently active item index
    };
  },
  computed: {
    // Computed properties to format the time in 'minutes:seconds'
    formattedCurrentTime() {
      return this.formatTime(this.currentTime);
    },
    formattedDuration() {
      return this.formatTime(this.duration);
    },
  },
  methods: {
    // Event handler when video metadata is loaded
    onLoadedMetadata() {
      const video = this.$refs.videoPlayer;
      this.duration = video.duration;
    },
    // Event handler to update current time as video plays
    onTimeUpdate() {
      const video = this.$refs.videoPlayer;
      this.currentTime = video.currentTime;

      // Update the active item based on the current time
      this.updateActiveItem();
    },
    // Format time into minutes:seconds
    formatTime(time) {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    },
    // Update the active item based on current time
    updateActiveItem() {
      // Find the next item based on the current time
      const nextItemIndex = this.items.findIndex(
        (item) => this.convertTimeString(item.uttstart_time) > this.currentTime
      );

      // Determine the current item index, with safety check
      const currentItemIndex =
        nextItemIndex > 0 ? nextItemIndex - 1 : this.items.length - 1;

      // Update only if the current item has changed
      if (this.currentItemIndex !== currentItemIndex && currentItemIndex >= 0) {
        this.currentItemIndex = currentItemIndex;
        this.scrollToActiveItem();
      }
    },
    // Auto-scroll the active item into view using scrollIntoView
    scrollToActiveItem() {
      const listItems = this.$refs.listItems;

      // Check if the active item exists before trying to scroll
      if (listItems && listItems[this.currentItemIndex]) {
        const activeItem = listItems[this.currentItemIndex];

        // Check if the current item is the first or last item to prevent unnecessary scrolling
        if (
          this.currentItemIndex === 0 ||
          this.currentItemIndex === this.items.length - 1
        ) {
          // Prevent scrolling for the first and last items
          return;
        }
        // Use scrollIntoView to center the active item
        activeItem.scrollIntoView({
          behavior: 'smooth', // Smooth scrolling
          block: 'start', // Center the item vertically
        });
      }
    },
    convertTimeString(timeString) {
      const [hours, minutes, seconds] = timeString.split(':').map(Number);

      // Create a Date object (optional)
      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      date.setSeconds(seconds);

      return {
        date,
        totalSeconds: hours * 3600 + minutes * 60 + seconds,
      };
    },
    convertTimeString(timeString = '') {
      const [hours, minutes, seconds] = timeString.split(':').map(Number);
      return hours * 3600 + minutes * 60 + seconds;
    },
    // Check if the item should be active based on the current video time
    isActive(item) {
      return (
        this.convertTimeString(this.items[this.currentItemIndex]?.uttstart_time) ===
        this.convertTimeString(item?.uttstart_time)
      );
    },
  },
};
</script>

<style scoped>
.scroll-container {
  max-height: 100px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 10px;
}

.scroll-container div {
  padding: 10px;
  transition: background-color 0.3s, font-weight 0.3s;
}

.scroll-container div.active {
  background-color: #f0f0f0;
  font-weight: bold;
  color: #333;
}
</style>
